/**
 * Codebase Documentation Orchestrator Agent
 * 
 * This agent coordinates comprehensive documentation generation for codebases
 * by analyzing structure and deploying specialized sub-agents for different
 * aspects of documentation.
 */

import { processWithAnthropic } from '../../tools/anthropic-ai';
import {
  PMOAgentOptions,
  PMOFormInput,
  PMOStreamUpdate,
  AgenticTeamId,
} from './PMOInterfaces';
import {
  CodebaseDocumentationTeamAgentResult
} from './TeamAgentInterfaces';
import { v4 as uuidv4 } from 'uuid';
import { addAgentOutput } from '../../../lib/firebase/agentOutputs';
import { chartTool, ChartGenerationResult, CHART_TYPES } from '../../tools/chart-tool';

// Custom options interface for Codebase Documentation Orchestrator
export interface CodebaseDocumentationOrchestratorOptions {
  userId: string;
  includeExplanation?: boolean;
  streamResponse?: boolean;
  onStreamUpdate?: (update: CodebaseDocumentationStreamUpdate) => void;
  codebasePaths?: string[];
  documentationScope?: 'full' | 'partial' | 'specific';
  outputFormat?: 'markdown' | 'html' | 'pdf';
  includeArchitecture?: boolean;
  includeApiDocs?: boolean;
  includeDataFlow?: boolean;
  maxSubAgents?: number;
}

// Sub-agent types for specialized documentation tasks
export enum SubAgentType {
  ProjectOverviewAnalyst = 'project-overview-analyst',
  ArchitectureAnalyst = 'architecture-analyst', 
  ComponentMapper = 'component-mapper',
  BusinessLogicAnalyst = 'business-logic-analyst',
  DataFlowAnalyst = 'data-flow-analyst',
  TechnicalEnvironmentAnalyst = 'technical-environment-analyst',
  DependenciesAnalyst = 'dependencies-analyst',
  APIDocumentationSpecialist = 'api-documentation-specialist',
  ConfigurationAnalyst = 'configuration-analyst'
}

export interface SubAgentAssignment {
  agentType: SubAgentType;
  assignment: string;
  priority: 'high' | 'medium' | 'low';
  estimatedComplexity: 'simple' | 'moderate' | 'complex';
  requiredPaths: string[];
}

export interface SubAgentResult {
  agentType: SubAgentType;
  assignment: string;
  output: string;
  success: boolean;
  error?: string;
  artifacts?: {
    diagrams?: string[];
    codeSnippets?: string[];
    configurations?: string[];
  };
}

export interface CodebaseAnalysisResult {
  totalFiles: number;
  totalLines: number;
  languages: string[];
  complexity: 'low' | 'medium' | 'high';
  mainDirectories: string[];
  keyFiles: string[];
  frameworks: string[];
  dependencies: string[];
}

export interface CodebaseDocumentationStreamUpdate {
  stage: 'analyzing-codebase' | 'deploying-sub-agents' | 'processing-assignments' | 'consolidating-results' | 'generating-final-docs' | 'complete';
  data?: any;
  message?: string;
  subAgentProgress?: {
    completed: number;
    total: number;
    currentAgent?: SubAgentType;
  };
}

export class CodebaseDocumentationOrchestratorAgent {
  private options: CodebaseDocumentationOrchestratorOptions;
  private subAgentResults: Map<SubAgentType, SubAgentResult> = new Map();

  constructor(options: CodebaseDocumentationOrchestratorOptions) {
    this.options = {
      userId: options.userId,
      includeExplanation: options.includeExplanation || false,
      streamResponse: options.streamResponse || false,
      onStreamUpdate: options.onStreamUpdate,
      codebasePaths: options.codebasePaths || [],
      documentationScope: options.documentationScope || 'full',
      outputFormat: options.outputFormat || 'markdown',
      includeArchitecture: options.includeArchitecture !== false,
      includeApiDocs: options.includeApiDocs !== false,
      includeDataFlow: options.includeDataFlow !== false,
      maxSubAgents: options.maxSubAgents || 9
    };

    if (!this.options.userId) {
      throw new Error("CodebaseDocumentationOrchestratorAgent requires a userId in options.");
    }
  }

  /**
   * Process codebase documentation request
   */
  public async processDocumentationRequest(
    selectedPaths: string[],
    description: string,
    customContext?: string
  ): Promise<CodebaseDocumentationTeamAgentResult> {
    try {
      this._updateStream('analyzing-codebase', {}, 'Analyzing codebase structure and complexity...');

      // Step 1: Analyze the codebase
      const codebaseAnalysis = await this._analyzeCodebase(selectedPaths);

      this._updateStream('deploying-sub-agents', { codebaseAnalysis }, 'Deploying specialized sub-agents...');

      // Step 2: Create sub-agent assignments
      const subAgentAssignments = await this._createSubAgentAssignments(
        selectedPaths,
        description,
        codebaseAnalysis,
        customContext
      );

      this._updateStream('processing-assignments', { 
        subAgentProgress: { completed: 0, total: subAgentAssignments.length }
      }, 'Processing sub-agent assignments...');

      // Step 3: Execute sub-agent assignments
      const subAgentResults = await this._executeSubAgentAssignments(subAgentAssignments);

      this._updateStream('consolidating-results', { subAgentResults }, 'Consolidating documentation results...');

      // Step 4: Generate final consolidated documentation
      const documentationArtifacts = await this._consolidateDocumentation(
        subAgentResults,
        codebaseAnalysis,
        description
      );

      this._updateStream('generating-final-docs', { documentationArtifacts }, 'Generating final documentation...');

      // Step 5: Generate supporting charts and diagrams
      const supportingCharts = await this._generateDocumentationCharts(
        codebaseAnalysis,
        documentationArtifacts
      );

      this._updateStream('complete', {}, 'Documentation generation complete!');

      return {
        success: true,
        taskId: uuidv4(),
        output: this._formatFinalOutput(documentationArtifacts, codebaseAnalysis),
        outputDocumentIds: [], // Will be populated when saved to Firebase
        documentationArtifacts,
        subAgentResults: Array.from(subAgentResults.values()),
        codebaseMetrics: codebaseAnalysis
      };

    } catch (error: any) {
      console.error('CodebaseDocumentationOrchestratorAgent: Error processing request:', error);
      return {
        success: false,
        taskId: uuidv4(),
        output: '',
        outputDocumentIds: [],
        error: error.message || 'Failed to process codebase documentation request'
      };
    }
  }

  /**
   * Analyze codebase structure and complexity
   */
  private async _analyzeCodebase(selectedPaths: string[]): Promise<CodebaseAnalysisResult> {
    // In a real implementation, this would:
    // 1. Scan the file system for the selected paths
    // 2. Analyze file types, sizes, and structure
    // 3. Detect frameworks and dependencies
    // 4. Calculate complexity metrics

    // Mock implementation for now
    return {
      totalFiles: 150,
      totalLines: 25000,
      languages: ['TypeScript', 'JavaScript', 'CSS', 'HTML'],
      complexity: 'medium',
      mainDirectories: ['src', 'components', 'lib', 'app'],
      keyFiles: ['package.json', 'tsconfig.json', 'next.config.js'],
      frameworks: ['Next.js', 'React', 'Tailwind CSS'],
      dependencies: ['react', 'next', 'typescript', 'tailwindcss']
    };
  }

  /**
   * Create specialized sub-agent assignments based on codebase analysis
   */
  private async _createSubAgentAssignments(
    selectedPaths: string[],
    description: string,
    codebaseAnalysis: CodebaseAnalysisResult,
    customContext?: string
  ): Promise<SubAgentAssignment[]> {
    const prompt = `
You are a Codebase Documentation Orchestrator Agent. Based on the following codebase analysis 
and user requirements, create specific assignments for specialized sub-agents.

CODEBASE ANALYSIS:
- Total Files: ${codebaseAnalysis.totalFiles}
- Languages: ${codebaseAnalysis.languages.join(', ')}
- Complexity: ${codebaseAnalysis.complexity}
- Frameworks: ${codebaseAnalysis.frameworks.join(', ')}
- Main Directories: ${codebaseAnalysis.mainDirectories.join(', ')}

USER REQUIREMENTS:
${description}

SELECTED PATHS:
${selectedPaths.join('\n')}

${customContext ? `ADDITIONAL CONTEXT:\n${customContext}` : ''}

Create assignments for the following sub-agents. Each assignment should be specific, actionable, and focused on the agent's specialty:

1. Project Overview Analyst - High-level project understanding and purpose
2. Architecture Analyst - System architecture and design patterns
3. Component Mapper - Component relationships and dependencies
4. Business Logic Analyst - Core business logic and workflows
5. Data Flow Analyst - Data flow and state management
6. Technical Environment Analyst - Development environment and tooling
7. Dependencies Analyst - External dependencies and integrations
8. API Documentation Specialist - API endpoints and interfaces
9. Configuration Analyst - Configuration files and environment setup

Return a JSON array of assignments with this structure:
{
  "agentType": "project-overview-analyst",
  "assignment": "Specific task description",
  "priority": "high|medium|low",
  "estimatedComplexity": "simple|moderate|complex",
  "requiredPaths": ["path1", "path2"]
}
`;

    try {
      const result = await processWithAnthropic({
        prompt,
        model: "claude-sonnet-4-20250514",
        modelOptions: {
          temperature: 0.3,
          maxTokens: 3000
        }
      });

      const assignments = JSON.parse(result.trim());
      return assignments.slice(0, this.options.maxSubAgents || 9);
    } catch (error: any) {
      console.error('Failed to create sub-agent assignments with Claude, using fallback:', error);
      return this._createFallbackAssignments(selectedPaths);
    }
  }

  /**
   * Execute all sub-agent assignments
   */
  private async _executeSubAgentAssignments(
    assignments: SubAgentAssignment[]
  ): Promise<Map<SubAgentType, SubAgentResult>> {
    const results = new Map<SubAgentType, SubAgentResult>();
    
    for (let i = 0; i < assignments.length; i++) {
      const assignment = assignments[i];
      
      this._updateStream('processing-assignments', {
        subAgentProgress: { 
          completed: i, 
          total: assignments.length, 
          currentAgent: assignment.agentType 
        }
      }, `Processing ${assignment.agentType}...`);

      try {
        const result = await this._executeSubAgentAssignment(assignment);
        results.set(assignment.agentType, result);
      } catch (error: any) {
        console.error(`Failed to execute ${assignment.agentType}:`, error);
        results.set(assignment.agentType, {
          agentType: assignment.agentType,
          assignment: assignment.assignment,
          output: '',
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Execute a single sub-agent assignment
   */
  private async _executeSubAgentAssignment(assignment: SubAgentAssignment): Promise<SubAgentResult> {
    const prompt = this._getSubAgentPrompt(assignment);

    try {
      const result = await processWithAnthropic({
        prompt,
        model: "claude-sonnet-4-20250514",
        modelOptions: {
          temperature: 0.4,
          maxTokens: 1500
        }
      });

      return {
        agentType: assignment.agentType,
        assignment: assignment.assignment,
        output: result,
        success: true
      };
    } catch (error: any) {
      throw new Error(`Sub-agent ${assignment.agentType} failed: ${error.message}`);
    }
  }

  /**
   * Get specialized prompt for each sub-agent type with enhanced Claude reasoning
   */
  private _getSubAgentPrompt(assignment: SubAgentAssignment): string {
    const basePrompt = `You are a ${assignment.agentType} specialized in codebase documentation with deep technical expertise.

ASSIGNMENT: ${assignment.assignment}

REQUIRED PATHS: ${assignment.requiredPaths.join(', ')}

Please analyze the codebase systematically and provide comprehensive documentation. Use your reasoning capabilities to:
1. Identify patterns and relationships
2. Infer architectural decisions and their rationale
3. Highlight potential issues or improvements
4. Provide context for technical choices

`;

    switch (assignment.agentType) {
      case SubAgentType.ProjectOverviewAnalyst:
        return basePrompt + `As a Project Overview Analyst, provide a comprehensive analysis including:

**Project Purpose & Goals:**
- What problem does this project solve?
- Who is the target audience?
- What are the key business objectives?

**Technical Overview:**
- What type of application/system is this?
- What are the main features and capabilities?
- How does it fit into the broader ecosystem?

**Project Scope & Scale:**
- Estimate the project complexity and maturity
- Identify the development stage (prototype, MVP, production)
- Assess the team size and development approach

Provide insights based on code structure, naming conventions, and architectural choices.`;

      case SubAgentType.ArchitectureAnalyst:
        return basePrompt + `As an Architecture Analyst, provide detailed architectural documentation:

**System Architecture:**
- Overall architectural pattern (MVC, microservices, monolith, etc.)
- Layer separation and responsibilities
- Key architectural decisions and their rationale

**Design Patterns:**
- Identify and document design patterns used
- Explain how patterns solve specific problems
- Note any anti-patterns or areas for improvement

**Structural Organization:**
- Module/package organization strategy
- Dependency management approach
- Separation of concerns implementation

**Scalability & Performance:**
- Architectural choices that impact scalability
- Performance considerations in the design
- Potential bottlenecks or limitations

Use your reasoning to infer the architectural philosophy and explain why certain decisions were made.`;

      case SubAgentType.ComponentMapper:
        return basePrompt + `As a Component Mapper, create a comprehensive component relationship analysis:

**Component Identification:**
- Identify all major components and modules
- Classify components by their role and responsibility
- Map component hierarchies and ownership

**Dependency Analysis:**
- Document direct and transitive dependencies
- Identify circular dependencies or coupling issues
- Map data flow between components

**Interface Documentation:**
- Document public APIs and interfaces between components
- Identify communication patterns (events, callbacks, direct calls)
- Note any abstraction layers or adapters

**Component Lifecycle:**
- How components are initialized and destroyed
- State management within and between components
- Error handling and recovery mechanisms

Reason about the component design choices and their implications for maintainability and extensibility.`;

      case SubAgentType.BusinessLogicAnalyst:
        return basePrompt + `As a Business Logic Analyst, document the core business functionality:

**Domain Logic:**
- Identify the core business domain and entities
- Document business rules and constraints
- Map business processes to code implementation

**Workflow Analysis:**
- Document key user workflows and use cases
- Identify decision points and business logic branches
- Map error handling and edge cases

**Business Rules:**
- Extract and document business validation rules
- Identify configuration-driven vs. hard-coded logic
- Note any regulatory or compliance requirements

**Data Processing:**
- How business data is processed and transformed
- Business calculations and algorithms
- Integration with external business systems

Use your analytical skills to understand the business context and explain how technical implementation serves business needs.`;

      case SubAgentType.DataFlowAnalyst:
        return basePrompt + `As a Data Flow Analyst, provide comprehensive data flow documentation:

**Data Architecture:**
- Identify data sources and destinations
- Document data models and schemas
- Map data transformation points

**State Management:**
- How application state is managed and persisted
- State synchronization between components
- Caching strategies and data consistency

**Data Processing Pipelines:**
- Input validation and sanitization
- Data transformation and enrichment
- Output formatting and delivery

**Performance & Optimization:**
- Data access patterns and optimization
- Caching strategies and invalidation
- Database queries and indexing considerations

Analyze the data flow patterns and reason about performance implications and potential improvements.`;

      case SubAgentType.TechnicalEnvironmentAnalyst:
        return basePrompt + `As a Technical Environment Analyst, document the development and deployment environment:

**Development Environment:**
- Required tools and their versions
- Development workflow and processes
- Local development setup instructions

**Build & Deployment:**
- Build system configuration and processes
- Deployment strategies and environments
- CI/CD pipeline documentation

**Infrastructure Requirements:**
- Runtime environment specifications
- External service dependencies
- Monitoring and logging setup

**Development Practices:**
- Code quality tools and standards
- Testing strategies and frameworks
- Documentation and maintenance practices

Reason about the technical choices and their impact on development productivity and system reliability.`;

      case SubAgentType.DependenciesAnalyst:
        return basePrompt + `As a Dependencies Analyst, provide thorough dependency analysis:

**External Dependencies:**
- List all external libraries and frameworks
- Document version constraints and compatibility
- Identify critical vs. optional dependencies

**Dependency Management:**
- Package management strategy and tools
- Version pinning and update policies
- Dependency resolution and conflict handling

**Security & Licensing:**
- Security implications of dependencies
- License compatibility and compliance
- Vulnerability assessment and mitigation

**Integration Analysis:**
- How external services are integrated
- API contracts and service level agreements
- Fallback strategies and error handling

Use your analytical capabilities to assess dependency health and identify potential risks or optimization opportunities.`;

      case SubAgentType.APIDocumentationSpecialist:
        return basePrompt + `As an API Documentation Specialist, create comprehensive API documentation:

**API Inventory:**
- Identify all public APIs and endpoints
- Document REST, GraphQL, or other API types
- Map API versioning and evolution strategy

**Interface Documentation:**
- Request/response schemas and examples
- Authentication and authorization mechanisms
- Error codes and handling procedures

**Integration Guidance:**
- Client SDK or library recommendations
- Rate limiting and usage guidelines
- Best practices for API consumption

**API Design Analysis:**
- RESTful design principles adherence
- Consistency in naming and structure
- Extensibility and backward compatibility

Reason about API design choices and provide insights on usability and maintainability.`;

      case SubAgentType.ConfigurationAnalyst:
        return basePrompt + `As a Configuration Analyst, document configuration and setup requirements:

**Configuration Management:**
- Identify all configuration files and formats
- Document environment-specific settings
- Map configuration precedence and overrides

**Environment Setup:**
- Installation and setup procedures
- Environment variable requirements
- Database and service configuration

**Customization Options:**
- Available configuration parameters
- Feature flags and toggles
- Plugin or extension mechanisms

**Security Configuration:**
- Security-related configuration options
- Secrets management and best practices
- Access control and permission settings

Analyze configuration patterns and reason about their impact on system flexibility and security.`;

      default:
        return basePrompt + `Provide detailed documentation for your assigned area of expertise, using systematic analysis and reasoning to provide comprehensive insights.`;
    }
  }

  /**
   * Create fallback assignments if LLM generation fails
   */
  private _createFallbackAssignments(selectedPaths: string[]): SubAgentAssignment[] {
    return [
      {
        agentType: SubAgentType.ProjectOverviewAnalyst,
        assignment: 'Analyze project structure and provide high-level overview',
        priority: 'high',
        estimatedComplexity: 'simple',
        requiredPaths: selectedPaths.slice(0, 3)
      },
      {
        agentType: SubAgentType.ArchitectureAnalyst,
        assignment: 'Document system architecture and design patterns',
        priority: 'high',
        estimatedComplexity: 'complex',
        requiredPaths: selectedPaths
      }
      // Add more fallback assignments as needed
    ];
  }

  /**
   * Consolidate all sub-agent results into final documentation
   */
  private async _consolidateDocumentation(
    subAgentResults: Map<SubAgentType, SubAgentResult>,
    codebaseAnalysis: CodebaseAnalysisResult,
    originalDescription: string
  ): Promise<any> {
    // Consolidate results into structured documentation artifacts
    const artifacts: any = {};

    subAgentResults.forEach((result, agentType) => {
      if (result.success) {
        switch (agentType) {
          case SubAgentType.ProjectOverviewAnalyst:
            artifacts.projectOverview = result.output;
            break;
          case SubAgentType.ArchitectureAnalyst:
            artifacts.architectureAnalysis = result.output;
            break;
          case SubAgentType.ComponentMapper:
            artifacts.componentMapping = result.output;
            break;
          case SubAgentType.BusinessLogicAnalyst:
            artifacts.businessLogicAnalysis = result.output;
            break;
          case SubAgentType.DataFlowAnalyst:
            artifacts.dataFlowDiagram = result.output;
            break;
          case SubAgentType.TechnicalEnvironmentAnalyst:
            artifacts.technicalEnvironment = result.output;
            break;
          case SubAgentType.DependenciesAnalyst:
            artifacts.dependenciesAnalysis = result.output;
            break;
          case SubAgentType.APIDocumentationSpecialist:
            artifacts.apiDocumentation = result.output;
            break;
          case SubAgentType.ConfigurationAnalyst:
            artifacts.configurationAnalysis = result.output;
            break;
        }
      }
    });

    return artifacts;
  }

  /**
   * Generate supporting charts and diagrams
   */
  private async _generateDocumentationCharts(
    codebaseAnalysis: CodebaseAnalysisResult,
    artifacts: any
  ): Promise<ChartGenerationResult[]> {
    const charts: ChartGenerationResult[] = [];

    try {
      // Generate language distribution chart
      const languageChart = await chartTool.generateChart({
        type: CHART_TYPES.PIE,
        title: 'Language Distribution',
        data: codebaseAnalysis.languages.map(lang => ({
          label: lang,
          value: Math.floor(Math.random() * 100) + 1 // Mock data
        }))
      });

      if (languageChart.success) {
        charts.push(languageChart);
      }

      // Generate complexity overview chart
      const complexityChart = await chartTool.generateChart({
        type: CHART_TYPES.BAR,
        title: 'Codebase Metrics',
        data: [
          { label: 'Total Files', value: codebaseAnalysis.totalFiles },
          { label: 'Total Lines (thousands)', value: Math.floor(codebaseAnalysis.totalLines / 1000) }
        ]
      });

      if (complexityChart.success) {
        charts.push(complexityChart);
      }

    } catch (error) {
      console.warn('Failed to generate documentation charts:', error);
    }

    return charts;
  }

  /**
   * Format final output for display
   */
  private _formatFinalOutput(artifacts: any, codebaseAnalysis: CodebaseAnalysisResult): string {
    let output = `# Codebase Documentation\n\n`;
    
    output += `## Overview\n`;
    output += `- **Total Files**: ${codebaseAnalysis.totalFiles}\n`;
    output += `- **Total Lines**: ${codebaseAnalysis.totalLines.toLocaleString()}\n`;
    output += `- **Languages**: ${codebaseAnalysis.languages.join(', ')}\n`;
    output += `- **Complexity**: ${codebaseAnalysis.complexity}\n`;
    output += `- **Frameworks**: ${codebaseAnalysis.frameworks.join(', ')}\n\n`;

    if (artifacts.projectOverview) {
      output += `## Project Overview\n${artifacts.projectOverview}\n\n`;
    }

    if (artifacts.architectureAnalysis) {
      output += `## Architecture Analysis\n${artifacts.architectureAnalysis}\n\n`;
    }

    if (artifacts.componentMapping) {
      output += `## Component Mapping\n${artifacts.componentMapping}\n\n`;
    }

    if (artifacts.businessLogicAnalysis) {
      output += `## Business Logic Analysis\n${artifacts.businessLogicAnalysis}\n\n`;
    }

    if (artifacts.dataFlowDiagram) {
      output += `## Data Flow Analysis\n${artifacts.dataFlowDiagram}\n\n`;
    }

    if (artifacts.technicalEnvironment) {
      output += `## Technical Environment\n${artifacts.technicalEnvironment}\n\n`;
    }

    if (artifacts.dependenciesAnalysis) {
      output += `## Dependencies Analysis\n${artifacts.dependenciesAnalysis}\n\n`;
    }

    if (artifacts.apiDocumentation) {
      output += `## API Documentation\n${artifacts.apiDocumentation}\n\n`;
    }

    if (artifacts.configurationAnalysis) {
      output += `## Configuration Analysis\n${artifacts.configurationAnalysis}\n\n`;
    }

    return output;
  }

  /**
   * Update stream if streaming is enabled
   */
  private _updateStream(
    stage: CodebaseDocumentationStreamUpdate['stage'],
    data?: any,
    message?: string
  ): void {
    if (this.options.streamResponse && this.options.onStreamUpdate) {
      this.options.onStreamUpdate({
        stage,
        data,
        message
      });
    }
  }
}
